import { useState } from 'react'
import { Button, Input, Modal, Select, Space, Table } from 'antd'
import type { TableRowSelection } from 'antd/es/table/interface'
import { TENANTS } from '@/assets/mocks/tenant'

export default function HomePage() {
    return (
        <div className='h-full w-full'>
            <div className='h-36 w-full bg-white p-10 mb-5'>
                <Button>Get Report</Button>
            </div>

            <div className='w-full bg-white p-7'>
                <TenantList />
            </div>
        </div>
    )
}

const TenantList = () => {
    const [selectionType, setSelectionType] = useState('checkbox')
    const [tenants, setTenants] = useState(TENANTS)
    const [openResponsive, setOpenResponsive] = useState(false)

    const selectBefore = (
        <Select defaultValue='http://'>
            <Option value='http://'>http://</Option>
            <Option value='https://'>https://</Option>
        </Select>
    )
    const selectAfter = (
        <Select defaultValue='.com'>
            <Option value='.com'>.com</Option>
            <Option value='.jp'>.jp</Option>
            <Option value='.cn'>.cn</Option>
            <Option value='.org'>.org</Option>
        </Select>
    )

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name'
        },
        {
            title: 'Description',
            dataIndex: 'description'
        },
        {
            title: 'Access Url',
            dataIndex: 'accessUrl',
            render: (text: any) => (
                <a className='text-blue-500' href={text} target='_blank'>
                    {text}
                </a>
            )
        },
        {
            title: 'Action',
            key: 'action',
            render: (_: any, record: any) => (
                <Space size='middle'>
                    <Button
                        color='default'
                        variant='outlined'
                        onClick={() => {
                            navigator.clipboard.writeText(record.accessUrl)
                        }}
                    >
                        Copy
                    </Button>
                    <Button
                        type='link'
                        color='primary'
                        variant='outlined'
                        target='_blank'
                        href={record.accessUrl}
                    >
                        View
                    </Button>

                    <Button color='danger' variant='outlined'>
                        Delete
                    </Button>
                </Space>
            )
        }
    ]

    const rowSelection: TableRowSelection<any> = {
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            console.log(
                `selectedRowKeys: ${String(selectedRowKeys)}`,
                'selectedRows: ',
                selectedRows
            )
        },
        getCheckboxProps: (record: any) => ({
            disabled: record.name === 'Disabled User', // Column configuration not to be checked
            name: record.name
        })
    }
    return (
        <div className='w-full bg-white'>
            <div className='flex justify-end mb-5'>
                <Button
                    onClick={() => {
                        setOpenResponsive(true)
                    }}
                >
                    Add Tenant
                </Button>
                <Modal
                    title='Create new Tenant'
                    centered
                    open={openResponsive}
                    onOk={() => {
                        setOpenResponsive(false)
                    }}
                    onCancel={() => {
                        setOpenResponsive(false)
                    }}
                    okText='Create'
                    width={{
                        xs: '90%',
                        sm: '80%',
                        md: '70%',
                        lg: '60%',
                        xl: '50%',
                        xxl: '40%'
                    }}
                >
                    <div>
                        <form>
                            <div className='mb-5'>
                                <label htmlFor='inp-name' className='block mb-2'>
                                    Name
                                </label>
                                <Input
                                    id='inp-name'
                                    name='name'
                                    type='text'
                                    size='large'
                                    className='w-full border border-gray-300 p-2 rounded-md'
                                />
                            </div>
                            <div className='mb-5'>
                                <label htmlFor='inp-description' className='block mb-2'>
                                    Description
                                </label>
                                <Input.TextArea
                                    id='inp-description'
                                    name='description'
                                    className='w-full border border-gray-300 p-2 rounded-md'
                                />
                            </div>
                            <div className='mb-5'>
                                <label className='block mb-2'>Access Url</label>
                                <Input
                                    size='large'
                                    type='text'
                                    className='w-full border border-gray-300 p-2 rounded-md'
                                    addonBefore={selectBefore}
                                    addonAfter={selectAfter}
                                    defaultValue='mysite'
                                />
                            </div>
                        </form>
                    </div>
                </Modal>
            </div>
            <Table
                rowSelection={Object.assign({ type: selectionType }, rowSelection)}
                columns={columns}
                dataSource={[...tenants]}
                scroll={{ x: 500, y: 500 }}
            />
        </div>
    )
}

import logo from '@/assets/images/core-vision.png'
import { Button } from 'antd'
import { useTranslation } from 'react-i18next'
import { IoHomeOutline } from 'react-icons/io5'
import { useNavigate } from 'react-router-dom'

export default function NotFoundPage() {
    const { t } = useTranslation()
    const navigate = useNavigate()

    return (
        <div className='flex items-center justify-center w-full h-full bg-white/30'>
            <div className='flex flex-col items-center gap-y-5'>
                <img
                    src={logo}
                    alt='Logo'
                    className='w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28 animate-spin-y'
                />

                <h2 className='text-primary'>
                    Core <b>Vision</b>
                </h2>

                <h3 className='italic text-gray-500 capitalize'>404 - Page not found</h3>

                <Button
                    className='!bg-gradient-to-r from-primary-600 via-primary-400 to-primary-300 !border-none'
                    size='large'
                    type='primary'
                    onClick={() => {
                        navigate('/')
                    }}
                    icon={<IoHomeOutline size={18} />}
                >
                    <h6>{t('button:goBackHome')}</h6>
                </Button>
            </div>
        </div>
    )
}

@import 'tailwindcss';
@import './font-face.css';
@import './animations.css';

* {
    box-sizing: border-box;
}

:root {
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@theme {
    --color-primary-100: #eef5fc;
    --color-primary-200: #dbedff;
    --color-primary-300: #28aae1;
    --color-primary-400: #2893e1;
    --color-primary-500: #0671e0;
    --color-primary-600: #2a3b8e;

    --color-success-100: #f1fbf8;
    --color-success-200: #84dfc1;
    --color-success-300: #32c997;
    --color-success-400: #009262;
    --color-success-500: #1b6e53;
    --color-success-600: #115b43;

    --color-warning-100: #fff8ec;
    --color-warning-200: #ffd596;
    --color-warning-300: #ffc670;
    --color-warning-400: #ffb240;
    --color-warning-500: #ffa826;
    --color-warning-600: #e48900;

    --color-danger-100: #fff1f0;
    --color-danger-200: #f0857d;
    --color-danger-300: #ff5a4f;
    --color-danger-400: #e02b1d;
    --color-danger-500: #e01507;
    --color-danger-600: #c33025;
}

@layer base {
    html,
    body,
    #root {
        @apply h-full m-0 p-0;
        @apply min-h-screen;
    }

    body {
        font-family: 'Montserrat', system-ui, sans-serif;
    }

    p {
        @apply mb-0 text-sm md:text-base;
    }

    h1 {
        @apply mb-0 text-3xl md:text-4xl lg:text-5xl;
    }

    h2 {
        @apply mb-0 text-2xl md:text-3xl lg:text-4xl;
    }

    h3 {
        @apply mb-0 text-xl md:text-2xl lg:text-3xl;
    }

    h4 {
        @apply mb-0 text-lg md:text-xl lg:text-2xl;
    }

    h5 {
        @apply mb-0 text-base md:text-lg lg:text-xl;
    }
}

@layer utilities {
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .small-scrollbar::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    .small-scrollbar::-webkit-scrollbar-thumb {
        background-color: #06b6d4;
    }

    .antd-placeholer-white {
        .ant-input::placeholder {
            color: #ffffff80 !important;
        }

        .ant-input-affix-wrapper .ant-input::placeholder {
            color: #ffffff80 !important;
        }
    }

    .text-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-300 inline-block text-transparent bg-clip-text;
    }
}

import { Suspense } from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'

import { protectedRoutes, publicRoutes } from './routes'
import ProtectedRoute from './protected-route'

import { AppFallback } from '@/components/app'
import { MainLayout } from '@/layouts'

export default function AppRouter() {
    return (
        <Suspense fallback={<AppFallback />}>
            <Routes>
                <Route element={<ProtectedRoute />}>
                    <Route element={<MainLayout />}>
                        <Route index element={<Navigate to='/home' />} />
                        {protectedRoutes.map((route) => (
                            <Route key={route.path} path={route.path} element={route.element} />
                        ))}
                    </Route>
                </Route>

                {publicRoutes.map((route) => (
                    <Route key={route.path} path={route.path} element={route.element} />
                ))}
                <Route path='/*' element={<Navigate to='/404' replace />} />
            </Routes>
        </Suspense>
    )
}

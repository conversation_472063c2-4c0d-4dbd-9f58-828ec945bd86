import { Layout, Menu } from 'antd'
import { Outlet, useLocation, useNavigate } from 'react-router-dom'

import { FiUsers } from 'react-icons/fi'
import { IoHomeOutline } from 'react-icons/io5'
import { useTranslation } from 'react-i18next'

import { StorageService } from '@/services/storage'

const sidebars = [
    {
        key: `/home`,
        icon: <IoHomeOutline size={20} />,
        label: `Trang chủ`
    },
    {
        key: `/users`,
        icon: <FiUsers size={20} />,
        label: `Người dùng`
    }
]

export const MainLayout = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const location = useLocation()

    const handleLogout = () => {
        StorageService.remove('access-token')
        StorageService.remove('refresh-token')
        navigate('/login')
    }

    return (
        <div className='flex flex-col h-full'>
            <div className='p-5 bg-gradient-to-r from-primary-600 to-primary-400/50'>
                <div className='demo-logo bg-white' />
            </div>
            <Layout className='h-full'>
                <Layout.Sider theme='light' width={270} className='h-full overflow-y-auto p-2'>
                    <div className='flex flex-col h-full'>
                        <Menu
                            mode='inline'
                            defaultSelectedKeys={[location.pathname]}
                            defaultOpenKeys={[location.pathname]}
                            selectedKeys={[location.pathname]}
                            style={{ height: '100%', borderInlineEnd: 0 }}
                            items={[...sidebars]}
                            onClick={(e) => {
                                if (e.key.includes('/')) {
                                    navigate(e.key)
                                }
                            }}
                        />
                        <button
                            onClick={handleLogout}
                            className='w-full bg-primary-500 mb-5 p-2 rounded-md text-white'
                        >
                            {t('button:logout')}
                        </button>
                    </div>
                </Layout.Sider>
                <Layout.Content className='h-full overflow-y-auto p-5'>
                    <Outlet />
                </Layout.Content>
            </Layout>
        </div>
    )
}
